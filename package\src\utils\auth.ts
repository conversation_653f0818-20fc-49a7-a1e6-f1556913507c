interface Admin {
  email: string;
  password: string;
}

const ADMINS: Admin[] = [
  { email: '<EMAIL>', password: 'K9mX#vR8nQ2$pL7wE3tY' },
  { email: '<EMAIL>', password: 'B6fH*jN4sM9&qW1rA5uI' },
  { email: '<PERSON><PERSON><PERSON>d<PERSON><EMAIL>', password: 'D8cV#xZ2gT6$nK4mP9wQ' }
];

export const authenticate = (email: string, password: string): boolean => {
  const admin = ADMINS.find(a => a.email === email && a.password === password);
  if (admin) {
    localStorage.setItem('isAuthenticated', 'true');
    localStorage.setItem('userEmail', admin.email);
    return true;
  }
  return false;
};

export const isAuthenticated = (): boolean => {
  if (typeof window === 'undefined') return false;
  return localStorage.getItem('isAuthenticated') === 'true';
};

export const logout = (): void => {
  localStorage.removeItem('isAuthenticated');
  localStorage.removeItem('userEmail');
};

export const getCurrentUser = (): string | null => {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('userEmail');
};

export const isReceptionUser = (): boolean => {
  const userEmail = getCurrentUser();
  return userEmail === '<EMAIL>';
};

export const isDashboardUser = (): boolean => {
  const userEmail = getCurrentUser();
  return userEmail === '<EMAIL>' || userEmail === '<EMAIL>';
};

export const isRestrictedPage = (pathname: string): boolean => {
  const restrictedPages = [
    '/',  // Dashboard
    '/class-management/teachers',
    '/class-management/payments'
  ];
  // Only apply restrictions for reception user
  return isReceptionUser() && restrictedPages.includes(pathname);
};